/* License Purchase Dialog - Compact Design */

/* Dialog Container */
.license-purchase-dialog {
  background-color: var(--card-bg-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  overflow: hidden;
  animation: zoomIn 0.3s var(--transition-timing);
}

/* Form Sections */
.form-section {
  margin-bottom: 1.5rem;
  animation: slideInUp 0.3s ease-out;
  animation-fill-mode: both;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title i {
  color: var(--primary-color);
}

/* Email Input */
.email-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-muted);
  z-index: 1;
}

/* User Validation */
.user-validation {
  margin-top: 0.75rem;
  padding: 0.75rem;
  border-radius: var(--border-radius-md);
  font-size: 0.9rem;
}

.validation-loading {
  display: flex;
  align-items: center;
  color: var(--text-muted);
}

.validation-success {
  display: flex;
  align-items: center;
  color: #198754;
  background-color: rgba(25, 135, 84, 0.1);
  border: 1px solid rgba(25, 135, 84, 0.2);
}

.validation-error {
  display: flex;
  align-items: center;
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
}

/* Select Wrapper */
.select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-select {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 1rem;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.modern-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

/* Package Info */
.package-info {
  margin-top: 1rem;
}

.info-card {
  background-color: var(--input-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 1rem;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.info-header h6 {
  margin: 0;
  font-weight: 600;
  color: var(--text-color);
}

.price {
  font-weight: 700;
  font-size: 1.1rem;
  color: var(--accent-color);
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--text-color);
}

.detail-item i {
  color: var(--primary-color);
  width: 16px;
}

.detail-description {
  font-size: 0.85rem;
  color: var(--text-muted);
  margin-top: 0.5rem;
  font-style: italic;
}

/* Package Grid */
.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.package-card {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--card-bg-color);
  position: relative;
}

.package-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.package-card.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.package-name {
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
}

.package-price {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--accent-color);
}

.package-card.selected .package-price {
  color: rgba(255, 255, 255, 0.9);
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.package-role,
.package-duration {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  font-weight: 500;
}

.package-card.selected .package-role,
.package-card.selected .package-duration {
  color: rgba(255, 255, 255, 0.9);
}

.package-description {
  font-size: 0.85rem;
  opacity: 0.8;
  margin-top: 0.5rem;
}

/* Payment Options */
.payment-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--card-bg-color);
  position: relative;
}

.payment-option:hover {
  border-color: var(--primary-color);
  transform: translateX(4px);
}

.payment-option.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.option-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.payment-option.selected .option-icon {
  background-color: rgba(255, 255, 255, 0.2);
}

.option-label {
  font-weight: 500;
  flex: 1;
}

.option-check {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
}

/* Modern Form Controls */
.modern-form-group {
  margin-bottom: 1rem;
}

.modern-label {
  display: block;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.modern-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

/* Loading States */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  color: var(--text-muted);
}

/* Form Errors */
.form-errors {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  margin-bottom: 1rem;
}

.error-item {
  color: #dc3545;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.error-item:last-child {
  margin-bottom: 0;
}

/* Animations */
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .license-purchase-dialog {
    max-width: 95vw;
    margin: 1rem;
  }

  .modern-card-footer {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modern-btn {
    width: 100%;
  }

  .info-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .license-purchase-dialog {
    margin: 0.5rem;
  }

  .form-section {
    margin-bottom: 1rem;
  }

  .modern-input,
  .modern-select {
    padding: 0.5rem 0.75rem 0.5rem 2rem;
  }

  .payment-option {
    padding: 0.75rem;
  }

  .option-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }
}

/* Focus States for Accessibility */
.payment-option:focus,
.modern-input:focus,
.modern-select:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .payment-option,
  .info-card {
    border-width: 3px;
  }

  .payment-option.selected {
    border-width: 4px;
  }
}