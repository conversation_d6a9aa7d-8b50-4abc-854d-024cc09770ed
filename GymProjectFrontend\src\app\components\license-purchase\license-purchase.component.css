/* License Purchase Dialog - Modern System Integration */

/* Dialog Container */
.license-purchase-dialog {
  background-color: var(--card-bg-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
  animation: zoomIn 0.3s var(--transition-timing);
}

/* Form Sections */
.form-section {
  margin-bottom: 2rem;
  animation: slideInUp 0.3s ease-out;
  animation-fill-mode: both;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title i {
  color: var(--primary-color);
}

/* Search Container */
.search-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-muted);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

/* User Grid */
.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 0.5rem;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--card-bg-color);
  position: relative;
}

.user-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.user-card.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.2rem;
  color: white;
  flex-shrink: 0;
}

.user-card.selected .user-avatar {
  background-color: rgba(255, 255, 255, 0.2);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
  font-size: 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  font-size: 0.85rem;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selection-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
}

/* Package Grid */
.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.package-card {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--card-bg-color);
  position: relative;
}

.package-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.package-card.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.package-name {
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
}

.package-price {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--accent-color);
}

.package-card.selected .package-price {
  color: rgba(255, 255, 255, 0.9);
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.package-role,
.package-duration {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  font-weight: 500;
}

.package-card.selected .package-role,
.package-card.selected .package-duration {
  color: rgba(255, 255, 255, 0.9);
}

.package-description {
  font-size: 0.85rem;
  opacity: 0.8;
  margin-top: 0.5rem;
}

/* Payment Methods */
.payment-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.payment-method {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--card-bg-color);
  position: relative;
}

.payment-method:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.payment-method.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.payment-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.payment-method.selected .payment-icon {
  background-color: rgba(255, 255, 255, 0.2);
}

.payment-label {
  font-weight: 500;
  text-align: center;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--input-bg);
  border-radius: var(--border-radius-md);
}

.pagination-info {
  color: var(--text-muted);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.pagination-btn {
  width: 36px;
  height: 36px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  background-color: var(--card-bg-color);
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-weight: 500;
  color: var(--text-color);
}

/* Loading and Empty States */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-muted);
}

.no-users,
.no-packages {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
  background-color: var(--input-bg);
  border-radius: var(--border-radius-md);
  border: 2px dashed var(--border-color);
}

/* Form Errors */
.form-errors {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  margin-bottom: 1rem;
}

.error-item {
  color: #dc3545;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.error-item:last-child {
  margin-bottom: 0;
}

/* Animations */
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .license-purchase-dialog {
    max-width: 95vw;
    margin: 1rem;
  }

  .user-grid {
    grid-template-columns: 1fr;
    max-height: 300px;
  }

  .package-grid {
    grid-template-columns: 1fr;
  }

  .payment-methods {
    grid-template-columns: 1fr;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .modern-card-footer {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modern-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .user-card {
    padding: 0.75rem;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .package-card {
    padding: 1rem;
  }

  .payment-method {
    padding: 1rem 0.75rem;
  }

  .search-input {
    padding: 0.5rem 0.75rem 0.5rem 2rem;
  }
}

/* Scrollbar Styling */
.user-grid::-webkit-scrollbar {
  width: 6px;
}

.user-grid::-webkit-scrollbar-track {
  background: var(--input-bg);
  border-radius: 3px;
}

.user-grid::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.user-grid::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Focus States for Accessibility */
.user-card:focus,
.package-card:focus,
.payment-method:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.pagination-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .user-card,
  .package-card,
  .payment-method {
    border-width: 3px;
  }

  .user-card.selected,
  .package-card.selected,
  .payment-method.selected {
    border-width: 4px;
  }
}