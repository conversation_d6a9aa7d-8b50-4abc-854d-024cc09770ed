/* Expired Licenses Component Styles */

/* Card Shadows */
.card {
  border: none;
  transition: all 0.3s ease;
}

.card.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Header Styling */
.card-header.bg-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  border-bottom: none;
}

.card-header h3 {
  font-weight: 600;
}

.card-header small {
  font-size: 0.875rem;
}

/* Table Styling */
.table {
  margin-bottom: 0;
}

.table-warning {
  background-color: #fff3cd;
  color: #664d03;
}

.table-warning th {
  border-color: #ffecb5;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 1rem 0.75rem;
}

.table tbody tr {
  transition: all 0.15s ease-in-out;
}

.table tbody tr:hover {
  background-color: rgba(255, 193, 7, 0.1);
}

.table td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border-color: #dee2e6;
}

/* Badge Styling */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
}

.badge.bg-info {
  background-color: #0dcaf0 !important;
  color: #000;
}

.badge.bg-secondary {
  background-color: #6c757d !important;
}

.badge.bg-danger {
  background-color: #dc3545 !important;
}

.badge.bg-warning {
  background-color: #ffc107 !important;
  color: #000;
}

.badge.bg-success {
  background-color: #198754 !important;
}

/* Button Styling */
.btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.btn-outline-success:hover {
  background-color: #198754;
  border-color: #198754;
}

/* Empty State */
.text-center i.fa-3x {
  font-size: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }
  
  .card-header .btn {
    width: 100%;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .btn-group-sm > .btn {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .card {
    background-color: #2d3748;
    color: #e2e8f0;
  }
  
  .card-header:not(.bg-warning) {
    background-color: #4a5568 !important;
    color: #e2e8f0;
  }
  
  .table {
    color: #e2e8f0;
  }
  
  .table-warning {
    background-color: #744210;
    color: #fef3c7;
  }
  
  .table-warning th {
    border-color: #92400e;
  }
  
  .table tbody tr:hover {
    background-color: rgba(245, 158, 11, 0.1);
  }
  
  .table td {
    border-color: #4a5568;
  }
  
  .text-muted {
    color: #a0aec0 !important;
  }
}

/* Custom Dark Mode Class Support */
[data-bs-theme="dark"] .card {
  background-color: #2d3748;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .card-header:not(.bg-warning) {
  background-color: #4a5568 !important;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .table {
  color: #e2e8f0;
}

[data-bs-theme="dark"] .table-warning {
  background-color: #744210;
  color: #fef3c7;
}

[data-bs-theme="dark"] .table-warning th {
  border-color: #92400e;
}

[data-bs-theme="dark"] .table tbody tr:hover {
  background-color: rgba(245, 158, 11, 0.1);
}

[data-bs-theme="dark"] .table td {
  border-color: #4a5568;
}

[data-bs-theme="dark"] .text-muted {
  color: #a0aec0 !important;
}
