<h2 mat-dialog-title>Lisans Uzat</h2>

<form [formGroup]="extendForm" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <div class="user-info mb-4 p-3 rounded">
      <h4 class="text-primary">{{ data.userLicense.userName }}</h4>
      <p class="text-muted mb-2">{{ data.userLicense.userEmail }}</p>
      <div class="row">
        <div class="col-6">
          <strong>Şirket:</strong> {{ data.userLicense.companyName }}<br>
          <strong>Paket:</strong> {{ data.userLicense.packageName }}
        </div>
        <div class="col-6">
          <strong>Rol:</strong> {{ data.userLicense.role }}<br>
          <strong>Bitiş:</strong> {{ data.userLicense.endDate | date:'dd/MM/yyyy' }}<br>
          <strong><PERSON><PERSON>:</strong> <span [class]="data.userLicense.remainingDays <= 7 ? 'text-danger fw-bold' : 'text-success'">{{ data.userLicense.remainingDays }} gün</span>
        </div>
      </div>
    </div>

    <!-- Extension Type Selection -->
    <div class="row mb-3">
      <div class="col-md-12">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Uzatma Türü</mat-label>
          <mat-select formControlName="extensionType">
            <mat-option value="package">Lisans Paketi ile Uzat</mat-option>
            <mat-option value="days">Manuel Gün Sayısı ile Uzat</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

    <!-- Package Selection -->
    <div class="row mb-3" *ngIf="extensionType === 'package'">
      <div class="col-md-12">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Lisans Paketi Seçin</mat-label>
          <mat-select formControlName="licensePackageID" [disabled]="isLoadingPackages">
            <mat-option *ngFor="let package of licensePackages" [value]="package.licensePackageID">
              {{ package.name }} - {{ package.durationDays }} gün ({{ package.price | currency:'TRY':'symbol':'1.0-0' }})
            </mat-option>
          </mat-select>
          <mat-error *ngIf="extendForm.get('licensePackageID')?.hasError('required')">
            Lütfen bir lisans paketi seçin
          </mat-error>
        </mat-form-field>

        <!-- Package Info -->
        <div *ngIf="getSelectedPackage()" class="alert alert-info mt-2">
          <strong>{{ getSelectedPackage()?.name }}</strong><br>
          <small>{{ getSelectedPackage()?.description }}</small><br>
          <strong>Süre:</strong> {{ getSelectedPackage()?.durationDays }} gün<br>
          <strong>Fiyat:</strong> {{ getSelectedPackage()?.price | currency:'TRY':'symbol':'1.0-0' }}
        </div>
      </div>
    </div>

    <!-- Manual Days Input -->
    <div class="row mb-3" *ngIf="extensionType === 'days'">
      <div class="col-md-12">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Uzatma Süresi (gün)</mat-label>
          <input matInput type="number" formControlName="extensionDays" min="1">
          <mat-error *ngIf="extendForm.get('extensionDays')?.hasError('required')">
            Uzatma süresi zorunludur
          </mat-error>
          <mat-error *ngIf="extendForm.get('extensionDays')?.hasError('min')">
            Uzatma süresi en az 1 gün olmalıdır
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Loading Packages -->
    <div *ngIf="isLoadingPackages" class="text-center p-3">
      <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2 text-muted">Lisans paketleri yükleniyor...</p>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()" [disabled]="isSubmitting">İptal</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="extendForm.invalid || isSubmitting">
      <span *ngIf="!isSubmitting">
        <i class="fas fa-calendar-plus me-2"></i>
        {{ extensionType === 'package' ? 'Paket ile Uzat' : 'Gün ile Uzat' }}
      </span>
      <span *ngIf="isSubmitting">
        <div class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        Uzatılıyor...
      </span>
    </button>
  </mat-dialog-actions>
</form>