<div class="modern-dialog zoom-in extend-license-dialog">
  <div class="modern-card-header">
    <h2><i class="fas fa-calendar-plus me-2"></i>Lisans Uzat</h2>
    <button class="modern-btn-icon" type="button" (click)="onCancel()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <form [formGroup]="extendForm" (ngSubmit)="onSubmit()">
    <div class="modern-card-body">
      <!-- User Info Section -->
      <div class="user-info-section mb-4">
        <div class="user-avatar">
          <div class="avatar-circle">
            {{ getUserInitials() }}
          </div>
        </div>
        <div class="user-details">
          <h4 class="user-name">{{ data.userLicense.userName }}</h4>
          <p class="user-email">{{ data.userLicense.userEmail }}</p>
          <div class="user-meta">
            <div class="meta-item">
              <i class="fas fa-building me-1"></i>
              <span>{{ data.userLicense.companyName }}</span>
            </div>
            <div class="meta-item">
              <i class="fas fa-box me-1"></i>
              <span>{{ data.userLicense.packageName }}</span>
            </div>
            <div class="meta-item">
              <i class="fas fa-user-tag me-1"></i>
              <span>{{ data.userLicense.role }}</span>
            </div>
          </div>
          <div class="license-status">
            <div class="status-item">
              <span class="status-label">Bitiş Tarihi:</span>
              <span class="status-value">{{ data.userLicense.endDate | date:'dd/MM/yyyy' }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">Kalan Süre:</span>
              <span class="status-value" [class]="getRemainingDaysClass()">
                {{ data.userLicense.remainingDays }} gün
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Extension Type Selection -->
      <div class="form-section mb-4">
        <h6 class="section-title">
          <i class="fas fa-cog me-2"></i>Uzatma Türü
        </h6>
        <div class="extension-type-selector">
          <div class="type-option"
               [class.active]="extensionType === 'package'"
               (click)="setExtensionType('package')">
            <div class="option-icon">
              <i class="fas fa-box"></i>
            </div>
            <div class="option-content">
              <h6>Lisans Paketi ile Uzat</h6>
              <small>Önceden tanımlanmış paketlerden seçin</small>
            </div>
          </div>
          <div class="type-option"
               [class.active]="extensionType === 'days'"
               (click)="setExtensionType('days')">
            <div class="option-icon">
              <i class="fas fa-calendar-day"></i>
            </div>
            <div class="option-content">
              <h6>Manuel Gün Sayısı ile Uzat</h6>
              <small>Özel gün sayısı belirleyin</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Package Selection -->
      <div class="form-section mb-4" *ngIf="extensionType === 'package'">
        <h6 class="section-title">
          <i class="fas fa-list me-2"></i>Lisans Paketi Seçimi
        </h6>

        <div *ngIf="isLoadingPackages" class="loading-state">
          <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
          <span class="ms-2">Paketler yükleniyor...</span>
        </div>

        <div *ngIf="!isLoadingPackages" class="package-list">
          <div class="package-item"
               *ngFor="let package of licensePackages"
               [class.selected]="extendForm.get('licensePackageID')?.value === package.licensePackageID"
               (click)="selectPackage(package.licensePackageID)">
            <div class="package-header">
              <h6 class="package-name">{{ package.name }}</h6>
              <span class="package-price">{{ package.price | currency:'TRY':'symbol':'1.0-0' }}</span>
            </div>
            <div class="package-details">
              <div class="package-duration">
                <i class="fas fa-clock me-1"></i>
                {{ package.durationDays }} gün
              </div>
              <div class="package-description">
                {{ package.description }}
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="!isLoadingPackages && licensePackages.length === 0" class="no-packages">
          <i class="fas fa-info-circle me-2"></i>
          Bu rol için uygun lisans paketi bulunamadı.
        </div>
      </div>

      <!-- Manual Days Input -->
      <div class="form-section mb-4" *ngIf="extensionType === 'days'">
        <h6 class="section-title">
          <i class="fas fa-calendar-alt me-2"></i>Uzatma Süresi
        </h6>
        <div class="modern-form-group">
          <label class="modern-label">Gün Sayısı</label>
          <input type="number"
                 class="modern-input"
                 formControlName="extensionDays"
                 min="1"
                 placeholder="Kaç gün uzatmak istiyorsunuz?">
          <div class="form-error" *ngIf="extendForm.get('extensionDays')?.hasError('required') && extendForm.get('extensionDays')?.touched">
            Uzatma süresi zorunludur
          </div>
          <div class="form-error" *ngIf="extendForm.get('extensionDays')?.hasError('min') && extendForm.get('extensionDays')?.touched">
            Uzatma süresi en az 1 gün olmalıdır
          </div>
        </div>
      </div>
    </div>

    <div class="modern-card-footer">
      <button type="button"
              class="modern-btn modern-btn-outline-secondary"
              (click)="onCancel()"
              [disabled]="isSubmitting">
        <i class="fas fa-times me-2"></i>İptal
      </button>
      <button type="submit"
              class="modern-btn modern-btn-primary"
              [disabled]="extendForm.invalid || isSubmitting">
        <span *ngIf="!isSubmitting">
          <i class="fas fa-calendar-plus me-2"></i>
          {{ extensionType === 'package' ? 'Paket ile Uzat' : 'Gün ile Uzat' }}
        </span>
        <span *ngIf="isSubmitting">
          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
          Uzatılıyor...
        </span>
      </button>
    </div>
  </form>
</div>