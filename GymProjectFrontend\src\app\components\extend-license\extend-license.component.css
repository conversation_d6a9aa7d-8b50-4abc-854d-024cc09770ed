/* Extend License Dialog Styles */

/* User Info Section */
.user-info {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.user-info h4 {
  color: #007bff;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.user-info .text-muted {
  color: #6c757d !important;
}

.user-info .text-danger {
  color: #dc3545 !important;
}

.user-info .text-success {
  color: #198754 !important;
}

.user-info .fw-bold {
  font-weight: 600 !important;
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
  background-color: #fff;
  color: #495057;
}

.form-control:focus, .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  background-color: #fff;
}

.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
}

/* Alert Styles */
.alert {
  border: none;
  border-radius: 0.5rem;
  padding: 1rem;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border-left: 4px solid #0dcaf0;
}

/* Loading Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Button Styles */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #004085;
}

.btn-primary:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .user-info {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }

  .user-info h4 {
    color: #63b3ed;
  }

  .user-info .text-muted {
    color: #a0aec0 !important;
  }

  .form-control, .form-select {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }

  .form-control:focus, .form-select:focus {
    background-color: #4a5568;
    border-color: #63b3ed;
    color: #e2e8f0;
    box-shadow: 0 0 0 0.25rem rgba(99, 179, 237, 0.25);
  }

  .form-label {
    color: #e2e8f0;
  }

  .alert-info {
    background-color: #2a4a5c;
    color: #bee3f8;
    border-left-color: #63b3ed;
  }
}

/* Custom Dark Mode Class Support */
[data-bs-theme="dark"] .user-info {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .user-info h4 {
  color: #63b3ed;
}

[data-bs-theme="dark"] .user-info .text-muted {
  color: #a0aec0 !important;
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
  background-color: #4a5568;
  border-color: #718096;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .form-control:focus,
[data-bs-theme="dark"] .form-select:focus {
  background-color: #4a5568;
  border-color: #63b3ed;
  color: #e2e8f0;
  box-shadow: 0 0 0 0.25rem rgba(99, 179, 237, 0.25);
}

[data-bs-theme="dark"] .form-label {
  color: #e2e8f0;
}

[data-bs-theme="dark"] .alert-info {
  background-color: #2a4a5c;
  color: #bee3f8;
  border-left-color: #63b3ed;
}

/* Dialog Specific Styles */
::ng-deep .mat-dialog-container {
  padding: 0 !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
}

::ng-deep .mat-dialog-title {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 1.5rem 2rem;
  margin: 0;
  font-weight: 600;
  font-size: 1.25rem;
}

::ng-deep .mat-dialog-content {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

::ng-deep .mat-dialog-actions {
  padding: 1.5rem 2rem;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  margin: 0;
  justify-content: flex-end;
  gap: 1rem;
}

/* Dark Mode Dialog Styles */
@media (prefers-color-scheme: dark) {
  ::ng-deep .mat-dialog-container {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important;
  }

  ::ng-deep .mat-dialog-actions {
    background-color: #1a202c !important;
    border-top-color: #4a5568 !important;
  }

  /* Angular Material Select Dark Mode Fix */
  ::ng-deep .mat-select-value {
    color: #e2e8f0 !important;
  }

  ::ng-deep .mat-select-arrow {
    color: #e2e8f0 !important;
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #718096 !important;
  }

  ::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: #63b3ed !important;
  }

  ::ng-deep .mat-form-field-label {
    color: #a0aec0 !important;
  }

  ::ng-deep .mat-form-field.mat-focused .mat-form-field-label {
    color: #63b3ed !important;
  }

  /* Dropdown Panel */
  ::ng-deep .mat-select-panel {
    background-color: #4a5568 !important;
  }

  ::ng-deep .mat-option {
    color: #e2e8f0 !important;
  }

  ::ng-deep .mat-option:hover {
    background-color: #2d3748 !important;
  }

  ::ng-deep .mat-option.mat-selected {
    background-color: #63b3ed !important;
    color: #1a202c !important;
  }

  ::ng-deep .mat-option.mat-selected:not(.mat-option-multiple) {
    background-color: #63b3ed !important;
  }

  /* Input Fields Dark Mode */
  ::ng-deep .mat-input-element {
    color: #e2e8f0 !important;
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
    background-color: transparent !important;
  }
}

[data-bs-theme="dark"] ::ng-deep .mat-dialog-container {
  background-color: #2d3748 !important;
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-dialog-actions {
  background-color: #1a202c !important;
  border-top-color: #4a5568 !important;
}

/* Angular Material Select Dark Mode Fix for data-bs-theme */
[data-bs-theme="dark"] ::ng-deep .mat-select-value {
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-select-arrow {
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: #718096 !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: #63b3ed !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-form-field-label {
  color: #a0aec0 !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-form-field.mat-focused .mat-form-field-label {
  color: #63b3ed !important;
}

/* Dropdown Panel for data-bs-theme */
[data-bs-theme="dark"] ::ng-deep .mat-select-panel {
  background-color: #4a5568 !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-option {
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-option:hover {
  background-color: #2d3748 !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-option.mat-selected {
  background-color: #63b3ed !important;
  color: #1a202c !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-option.mat-selected:not(.mat-option-multiple) {
  background-color: #63b3ed !important;
}

/* Input Fields Dark Mode for data-bs-theme */
[data-bs-theme="dark"] ::ng-deep .mat-input-element {
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] ::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
  background-color: transparent !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  ::ng-deep .mat-dialog-title {
    padding: 1rem 1.5rem;
    font-size: 1.125rem;
  }

  ::ng-deep .mat-dialog-content {
    padding: 1.5rem;
  }

  ::ng-deep .mat-dialog-actions {
    padding: 1rem 1.5rem;
    flex-direction: column;
  }

  ::ng-deep .mat-dialog-actions .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  ::ng-deep .mat-dialog-actions .btn:last-child {
    margin-bottom: 0;
  }
}

/* Animation */
.user-info {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading State */
.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Package Selection Highlight */
.alert-info {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}