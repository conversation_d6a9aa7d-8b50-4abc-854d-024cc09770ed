/* Extend License Dialog - Modern System Integration */

/* Dialog Container */
.extend-license-dialog {
  background-color: var(--card-bg-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  overflow: hidden;
  animation: zoomIn 0.3s var(--transition-timing);
}

/* User Info Section */
.user-info-section {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  border-radius: var(--border-radius-md);
  margin-bottom: 1.5rem;
}

.user-avatar .avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.5rem;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: white;
}

.user-email {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 1rem;
}

.user-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  opacity: 0.9;
}

.meta-item i {
  opacity: 0.8;
}

.license-status {
  display: flex;
  gap: 2rem;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.status-label {
  font-size: 0.75rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-value {
  font-weight: 600;
  font-size: 0.9rem;
}

.status-value.text-danger {
  color: #ff6b6b;
}

.status-value.text-warning {
  color: #ffd93d;
}

.status-value.text-success {
  color: #51cf66;
}

/* Form Sections */
.form-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title i {
  color: var(--primary-color);
}

/* Extension Type Selector */
.extension-type-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.type-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--card-bg-color);
}

.type-option:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.type-option.active {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.type-option.active .option-icon {
  background-color: rgba(255, 255, 255, 0.2);
}

.option-content h6 {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
}

.option-content small {
  opacity: 0.8;
  font-size: 0.8rem;
}

/* Package List */
.package-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.package-item {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--card-bg-color);
}

.package-item:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.package-item.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.package-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
}

.package-name {
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
}

.package-price {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--accent-color);
}

.package-item.selected .package-price {
  color: rgba(255, 255, 255, 0.9);
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.package-duration {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: var(--primary-color);
}

.package-item.selected .package-duration {
  color: rgba(255, 255, 255, 0.9);
}

.package-description {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Modern Form Controls */
.modern-form-group {
  margin-bottom: 1.5rem;
}

.modern-label {
  display: block;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.modern-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.form-error {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Loading and Empty States */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-muted);
}

.no-packages {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
  background-color: var(--input-bg);
  border-radius: var(--border-radius-md);
  border: 2px dashed var(--border-color);
}

/* Alert Styles */
.alert {
  border: none;
  border-radius: 0.5rem;
  padding: 1rem;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border-left: 4px solid #0dcaf0;
}

/* Loading Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Button Styles */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #004085;
}

.btn-primary:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .user-info {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }

  .user-info h4 {
    color: #63b3ed;
  }

  .user-info .text-muted {
    color: #a0aec0 !important;
  }

  .form-control, .form-select {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }

  .form-control:focus, .form-select:focus {
    background-color: #4a5568;
    border-color: #63b3ed;
    color: #e2e8f0;
    box-shadow: 0 0 0 0.25rem rgba(99, 179, 237, 0.25);
  }

  .form-label {
    color: #e2e8f0;
  }

  .alert-info {
    background-color: #2a4a5c;
    color: #bee3f8;
    border-left-color: #63b3ed;
  }
}

/* Custom Dark Mode Class Support */
[data-bs-theme="dark"] .user-info {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .user-info h4 {
  color: #63b3ed;
}

[data-bs-theme="dark"] .user-info .text-muted {
  color: #a0aec0 !important;
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
  background-color: #4a5568;
  border-color: #718096;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .form-control:focus,
[data-bs-theme="dark"] .form-select:focus {
  background-color: #4a5568;
  border-color: #63b3ed;
  color: #e2e8f0;
  box-shadow: 0 0 0 0.25rem rgba(99, 179, 237, 0.25);
}

[data-bs-theme="dark"] .form-label {
  color: #e2e8f0;
}

[data-bs-theme="dark"] .alert-info {
  background-color: #2a4a5c;
  color: #bee3f8;
  border-left-color: #63b3ed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .extend-license-dialog {
    max-width: 95vw;
    margin: 1rem;
  }

  .user-info-section {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .user-meta {
    justify-content: center;
  }

  .license-status {
    justify-content: center;
  }

  .extension-type-selector {
    grid-template-columns: 1fr;
  }

  .type-option {
    padding: 1rem;
  }

  .package-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .modern-card-footer {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modern-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .user-info-section {
    padding: 1rem;
  }

  .type-option {
    padding: 0.75rem;
  }

  .package-item {
    padding: 1rem;
  }
}

/* Animation Effects */
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-section {
  animation: slideInUp 0.3s ease-out;
  animation-fill-mode: both;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }

/* Responsive Design */
@media (max-width: 768px) {
  ::ng-deep .mat-dialog-title {
    padding: 1rem 1.5rem;
    font-size: 1.125rem;
  }

  ::ng-deep .mat-dialog-content {
    padding: 1.5rem;
  }

  ::ng-deep .mat-dialog-actions {
    padding: 1rem 1.5rem;
    flex-direction: column;
  }

  ::ng-deep .mat-dialog-actions .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  ::ng-deep .mat-dialog-actions .btn:last-child {
    margin-bottom: 0;
  }
}

/* Animation */
.user-info {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading State */
.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Package Selection Highlight */
.alert-info {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}