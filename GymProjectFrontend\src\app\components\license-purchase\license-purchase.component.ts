import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { LicensePackageService } from '../../services/license-package.service';
import { UserLicenseService } from '../../services/user-license.service';
import { LicensePackage } from '../../models/licensePackage';
import { LicensePurchaseDto } from '../../models/LicensePurchaseDto';
import { User } from '../../models/user';
import { UserService } from '../../services/user-service.service';

@Component({
  selector: 'app-license-purchase',
  templateUrl: './license-purchase.component.html',
  styleUrls: ['./license-purchase.component.css'],
  standalone:false
})
export class LicensePurchaseComponent implements OnInit {
  purchaseForm: FormGroup;
  licensePackages: LicensePackage[] = [];
  isLoading = false;
  isSubmitting = false;
  paymentMethods: string[] = ['Nakit', '<PERSON><PERSON><PERSON>', 'Havale/EFT'];

  // Email validation
  userEmail = '';
  emailValidationState: 'idle' | 'loading' | 'valid' | 'invalid' = 'idle';
  selectedUser: User | null = null;
  private emailValidationTimeout: any;
  
  constructor(
    private fb: FormBuilder,
    private licensePackageService: LicensePackageService,
    private userLicenseService: UserLicenseService,
    private userService: UserService,
    private toastr: ToastrService,
    public dialogRef: MatDialogRef<LicensePurchaseComponent>
  ) {
    this.purchaseForm = this.fb.group({
      userID: [null, Validators.required],
      licensePackageID: [null, Validators.required],
      paymentMethod: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadLicensePackages();
  }

  loadLicensePackages(): void {
    this.isLoading = true;
    this.licensePackageService.getAll().subscribe({
      next: (response) => {
        this.licensePackages = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Lisans paketleri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  /**
   * Email input değişikliği
   */
  onEmailInput(event: any): void {
    this.userEmail = event.target.value;

    // Clear previous timeout
    if (this.emailValidationTimeout) {
      clearTimeout(this.emailValidationTimeout);
    }

    // Reset validation state
    this.emailValidationState = 'idle';
    this.selectedUser = null;
    this.purchaseForm.patchValue({ userID: null });

    // Debounce validation
    if (this.userEmail && this.userEmail.length > 3) {
      this.emailValidationTimeout = setTimeout(() => {
        this.validateUserEmail();
      }, 500);
    }
  }

  /**
   * Email validasyonu
   */
  validateUserEmail(): void {
    if (!this.userEmail || this.userEmail.length < 3) {
      this.emailValidationState = 'idle';
      return;
    }

    this.emailValidationState = 'loading';

    // Email ile kullanıcı arama
    this.userService.getNonMembersPaginated(1, 100, this.userEmail).subscribe({
      next: (response) => {
        const user = response.data.find((u: any) => u.email.toLowerCase() === this.userEmail.toLowerCase());

        if (user) {
          this.selectedUser = user;
          this.emailValidationState = 'valid';
          this.purchaseForm.patchValue({ userID: user.userID });
        } else {
          this.selectedUser = null;
          this.emailValidationState = 'invalid';
          this.purchaseForm.patchValue({ userID: null });
        }
      },
      error: (error) => {
        this.emailValidationState = 'invalid';
        this.selectedUser = null;
        this.purchaseForm.patchValue({ userID: null });
      }
    });
  }

  /**
   * Seçili paketi getir
   */
  getSelectedPackage(): LicensePackage | null {
    const selectedId = this.purchaseForm.get('licensePackageID')?.value;
    if (!selectedId) return null;
    return this.licensePackages.find(pkg => pkg.licensePackageID === selectedId) || null;
  }

  /**
   * Form hatalarını kontrol et
   */
  hasFormErrors(): boolean {
    return !this.selectedUser ||
           (this.purchaseForm.get('licensePackageID')?.hasError('required') ?? false) ||
           (this.purchaseForm.get('paymentMethod')?.hasError('required') ?? false);
  }

  /**
   * Submit edilebilir mi kontrol et
   */
  canSubmit(): boolean {
    return this.selectedUser !== null &&
           this.purchaseForm.get('licensePackageID')?.value &&
           this.purchaseForm.get('paymentMethod')?.value &&
           this.emailValidationState === 'valid';
  }

  onSubmit(): void {
    if (this.purchaseForm.invalid) {
      this.toastr.error('Lütfen formu doğru şekilde doldurun', 'Hata');
      return;
    }

    if (!this.selectedUser) {
      this.toastr.error('Lütfen bir kullanıcı seçin', 'Hata');
      return;
    }

    this.isSubmitting = true;

    const formValue = this.purchaseForm.value;
    const licensePurchaseDto: LicensePurchaseDto = {
      userID: formValue.userID,
      licensePackageID: formValue.licensePackageID,
      paymentMethod: formValue.paymentMethod
    };

    this.userLicenseService.purchase(licensePurchaseDto).subscribe({
      next: (response) => {
        this.toastr.success(response.message, 'Başarılı');
        this.dialogRef.close(true);
        this.isSubmitting = false;
      },
      error: (error) => {
        this.toastr.error('Lisans satın alınırken bir hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
