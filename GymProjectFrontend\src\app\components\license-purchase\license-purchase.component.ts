import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { LicensePackageService } from '../../services/license-package.service';
import { UserLicenseService } from '../../services/user-license.service';
import { LicensePackage } from '../../models/licensePackage';
import { LicensePurchaseDto } from '../../models/LicensePurchaseDto';
import { User } from '../../models/user';
import { UserService } from '../../services/user-service.service';

@Component({
  selector: 'app-license-purchase',
  templateUrl: './license-purchase.component.html',
  styleUrls: ['./license-purchase.component.css'],
  standalone:false
})
export class LicensePurchaseComponent implements OnInit {
  purchaseForm: FormGroup;
  licensePackages: LicensePackage[] = [];
  users: User[] = [];
  isLoading = false;
  isSubmitting = false;
  paymentMethods: string[] = ['Nakit', '<PERSON><PERSON><PERSON>', 'Havale/EFT'];
  filteredUsers: User[] = [];
  filteredPackages: LicensePackage[] = [];

  // Pagination için yeni özellikler
  currentPage = 1;
  pageSize = 20;
  totalUsers = 0;
  totalPages = 0;
  searchTerm = '';
  isLoadingUsers = false;
  selectedUser: User | null = null;
  
  constructor(
    private fb: FormBuilder,
    private licensePackageService: LicensePackageService,
    private userLicenseService: UserLicenseService,
    private userService: UserService,
    private toastr: ToastrService,
    public dialogRef: MatDialogRef<LicensePurchaseComponent>
  ) {
    this.purchaseForm = this.fb.group({
      userID: [null, Validators.required],
      licensePackageID: [null, Validators.required],
      paymentMethod: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadLicensePackages();
    this.loadUsersWithPagination();
  }

  loadLicensePackages(): void {
    this.isLoading = true;
    this.licensePackageService.getAll().subscribe({
      next: (response) => {
        this.licensePackages = response.data;
        this.filteredPackages = this.licensePackages;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Lisans paketleri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  /**
   * 10K+ kullanıcı için optimize edilmiş sayfalı yükleme
   */
  loadUsersWithPagination(): void {
    this.isLoadingUsers = true;
    this.userService.getNonMembersPaginated(this.currentPage, this.pageSize, this.searchTerm).subscribe({
      next: (response) => {
        this.users = response.data;
        this.filteredUsers = this.users;
        this.totalUsers = response.totalCount;
        this.totalPages = response.totalPages;
        this.isLoadingUsers = false;
      },
      error: (error) => {
        this.toastr.error('Kullanıcılar yüklenirken bir hata oluştu', 'Hata');
        this.isLoadingUsers = false;
      }
    });
  }

  /**
   * Arama işlemi (debounced)
   */
  onSearchUsers(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.currentPage = 1; // Arama yapılınca ilk sayfaya dön
    this.loadUsersWithPagination();
  }

  /**
   * Sayfa değiştirme
   */
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.loadUsersWithPagination();
    }
  }

  /**
   * Kullanıcı seçimi
   */
  selectUser(user: User): void {
    this.selectedUser = user;
    this.purchaseForm.patchValue({ userID: user.userID });
  }

  /**
   * Kullanıcı başlangıç harflerini al
   */
  getUserInitials(user: User): string {
    if (!user.firstName && !user.lastName) return 'U';
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
  }

  /**
   * Paket seçimi
   */
  selectPackage(pkg: LicensePackage): void {
    this.purchaseForm.patchValue({ licensePackageID: pkg.licensePackageID });
  }

  filterPackages(value: string): void {
    const filterValue = value.toLowerCase();
    this.filteredPackages = this.licensePackages.filter(pkg => 
      pkg.name.toLowerCase().includes(filterValue) ||
      pkg.description.toLowerCase().includes(filterValue) ||
      pkg.role.toLowerCase().includes(filterValue)
    );
  }

  displayUser(user: User): string {
    return user ? `${user.firstName} ${user.lastName} (${user.email})` : '';
  }

  displayPackage(pkg: LicensePackage): string {
    return pkg ? `${pkg.name} - ${pkg.role} (${pkg.durationDays} gün)` : '';
  }

  onSubmit(): void {
    if (this.purchaseForm.invalid) {
      this.toastr.error('Lütfen formu doğru şekilde doldurun', 'Hata');
      return;
    }

    if (!this.selectedUser) {
      this.toastr.error('Lütfen bir kullanıcı seçin', 'Hata');
      return;
    }

    this.isSubmitting = true;

    const formValue = this.purchaseForm.value;
    const licensePurchaseDto: LicensePurchaseDto = {
      userID: formValue.userID,
      licensePackageID: formValue.licensePackageID,
      paymentMethod: formValue.paymentMethod
    };

    this.userLicenseService.purchase(licensePurchaseDto).subscribe({
      next: (response) => {
        this.toastr.success(response.message, 'Başarılı');
        this.dialogRef.close(true);
        this.isSubmitting = false;
      },
      error: (error) => {
        this.toastr.error('Lisans satın alınırken bir hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
