<div class="modern-dialog zoom-in license-purchase-dialog">
  <div class="modern-card-header">
    <h2><i class="fas fa-shopping-cart me-2"></i><PERSON><PERSON> Lisans Satın Al</h2>
    <button class="modern-btn-icon" type="button" (click)="onCancel()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <form [formGroup]="purchaseForm" (ngSubmit)="onSubmit()">
    <div class="modern-card-body">
      <!-- User Email Input -->
      <div class="form-section mb-3">
        <h6 class="section-title">
          <i class="fas fa-user me-2"></i>Kullanıcı Bilgileri
        </h6>
        <div class="modern-form-group">
          <label class="modern-label">E-posta Adresi</label>
          <div class="email-input-wrapper">
            <i class="fas fa-envelope input-icon"></i>
            <input type="email"
                   class="modern-input"
                   placeholder="Kullanıcının e-posta adresini girin..."
                   [matAutocomplete]="emailAuto"
                   [(ngModel)]="userEmail"
                   [ngModelOptions]="{standalone: true}"
                   (input)="onEmailInput($event)">
            <mat-autocomplete #emailAuto="matAutocomplete" [displayWith]="displayUserEmail">
              <mat-option *ngFor="let user of filteredUsers" [value]="user" (onSelectionChange)="selectUserFromAutocomplete(user, $event)">
                <div class="user-option">
                  <div class="user-avatar-small">{{ getUserInitials(user) }}</div>
                  <div class="user-details">
                    <div class="user-name">{{ user.firstName }} {{ user.lastName }}</div>
                    <div class="user-email">{{ user.email }}</div>
                  </div>
                </div>
              </mat-option>
            </mat-autocomplete>
          </div>
          <div class="user-validation" *ngIf="emailValidationState !== 'idle'">
            <div *ngIf="emailValidationState === 'loading'" class="validation-loading">
              <div class="spinner-border spinner-border-sm me-2" role="status"></div>
              <span>Kullanıcı kontrol ediliyor...</span>
            </div>
            <div *ngIf="emailValidationState === 'valid'" class="validation-success">
              <i class="fas fa-check-circle me-2"></i>
              <span>{{ selectedUser?.firstName }} {{ selectedUser?.lastName }}</span>
            </div>
            <div *ngIf="emailValidationState === 'invalid'" class="validation-error">
              <i class="fas fa-exclamation-circle me-2"></i>
              <span>Bu e-posta adresine sahip uygun kullanıcı bulunamadı</span>
            </div>
          </div>
        </div>
      </div>

      <!-- License Package Selection -->
      <div class="form-section mb-3">
        <h6 class="section-title">
          <i class="fas fa-box me-2"></i>Lisans Paketi
        </h6>
        <div class="modern-form-group">
          <label class="modern-label">Paket Seçimi</label>
          <div class="select-wrapper">
            <i class="fas fa-box input-icon"></i>
            <select class="modern-select" formControlName="licensePackageID">
              <option value="">Lisans paketi seçin...</option>
              <option *ngFor="let pkg of licensePackages" [value]="pkg.licensePackageID">
                {{ pkg.name }} - {{ pkg.role }} ({{ pkg.durationDays }} gün) - {{ pkg.price | currency:'TRY':'symbol':'1.0-0' }}
              </option>
            </select>
          </div>
          <div class="package-info" *ngIf="getSelectedPackage()">
            <div class="info-card">
              <div class="info-header">
                <h6>{{ getSelectedPackage()?.name }}</h6>
                <span class="price">{{ getSelectedPackage()?.price | currency:'TRY':'symbol':'1.0-0' }}</span>
              </div>
              <div class="info-details">
                <div class="detail-item">
                  <i class="fas fa-user-tag me-1"></i>
                  <span>{{ getSelectedPackage()?.role }}</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-clock me-1"></i>
                  <span>{{ getSelectedPackage()?.durationDays }} gün</span>
                </div>
                <div class="detail-description">
                  {{ getSelectedPackage()?.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment Method Selection -->
      <div class="form-section mb-3">
        <h6 class="section-title">
          <i class="fas fa-credit-card me-2"></i>Ödeme Yöntemi
        </h6>
        <div class="modern-form-group">
          <label class="modern-label">Ödeme Türü</label>
          <div class="payment-options">
            <div class="payment-option"
                 *ngFor="let method of paymentMethods"
                 [class.selected]="purchaseForm.get('paymentMethod')?.value === method"
                 (click)="purchaseForm.patchValue({paymentMethod: method})">
              <div class="option-icon">
                <i class="fas" [ngClass]="{
                  'fa-money-bill-alt': method === 'Nakit',
                  'fa-credit-card': method === 'Kredi Kartı',
                  'fa-university': method === 'Havale/EFT'
                }"></i>
              </div>
              <span class="option-label">{{ method }}</span>
              <div class="option-check" *ngIf="purchaseForm.get('paymentMethod')?.value === method">
                <i class="fas fa-check"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Validation Errors -->
      <div class="form-errors" *ngIf="hasFormErrors()">
        <div class="error-item" *ngIf="!selectedUser">
          <i class="fas fa-exclamation-circle me-2"></i>
          Lütfen geçerli bir kullanıcı e-postası girin
        </div>
        <div class="error-item" *ngIf="purchaseForm.get('licensePackageID')?.hasError('required')">
          <i class="fas fa-exclamation-circle me-2"></i>
          Lütfen bir lisans paketi seçin
        </div>
        <div class="error-item" *ngIf="purchaseForm.get('paymentMethod')?.hasError('required')">
          <i class="fas fa-exclamation-circle me-2"></i>
          Lütfen bir ödeme yöntemi seçin
        </div>
      </div>
    </div>

    <div class="modern-card-footer">
      <button type="button"
              class="modern-btn modern-btn-outline-secondary"
              (click)="onCancel()"
              [disabled]="isSubmitting">
        <i class="fas fa-times me-2"></i>İptal
      </button>
      <button type="submit"
              class="modern-btn modern-btn-primary"
              [disabled]="!canSubmit() || isSubmitting">
        <span *ngIf="!isSubmitting">
          <i class="fas fa-shopping-cart me-2"></i>Satın Al
        </span>
        <span *ngIf="isSubmitting">
          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
          Satın alınıyor...
        </span>
      </button>
    </div>
  </form>
</div>
