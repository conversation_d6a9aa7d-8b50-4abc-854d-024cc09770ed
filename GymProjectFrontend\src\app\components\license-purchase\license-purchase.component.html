<div class="modern-dialog zoom-in license-purchase-dialog">
  <div class="modern-card-header">
    <h2><i class="fas fa-shopping-cart me-2"></i><PERSON>ni Lisans Satın Al</h2>
    <button class="modern-btn-icon" type="button" (click)="onCancel()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <form [formGroup]="purchaseForm" (ngSubmit)="onSubmit()">
    <div class="modern-card-body">
      <!-- User Selection Section -->
      <div class="form-section mb-4">
        <h6 class="section-title">
          <i class="fas fa-users me-2"></i><PERSON>llanı<PERSON>ı Seçimi
        </h6>

        <!-- Search Bar -->
        <div class="search-container mb-3">
          <div class="search-input-wrapper">
            <i class="fas fa-search search-icon"></i>
            <input type="text"
                   class="search-input"
                   placeholder="Kullanıc<PERSON> adı veya e-posta arayın..."
                   (input)="onSearchUsers($any($event.target).value)"
                   [value]="searchTerm">
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="isLoadingUsers" class="loading-state">
          <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
          <span class="ms-2">Kullanıcılar yükleniyor...</span>
        </div>

        <!-- User Grid -->
        <div *ngIf="!isLoadingUsers" class="user-grid">
          <div class="user-card"
               *ngFor="let user of users"
               [class.selected]="selectedUser?.userID === user.userID"
               (click)="selectUser(user)">
            <div class="user-avatar">
              {{ getUserInitials(user) }}
            </div>
            <div class="user-info">
              <h6 class="user-name">{{ user.firstName }} {{ user.lastName }}</h6>
              <small class="user-email">{{ user.email }}</small>
            </div>
            <div class="selection-indicator" *ngIf="selectedUser?.userID === user.userID">
              <i class="fas fa-check"></i>
            </div>
          </div>
        </div>

        <!-- No Users Found -->
        <div *ngIf="!isLoadingUsers && users.length === 0" class="no-users">
          <i class="fas fa-user-slash me-2"></i>
          <span *ngIf="searchTerm">Arama kriterinize uygun kullanıcı bulunamadı.</span>
          <span *ngIf="!searchTerm">Lisans satın alabilecek kullanıcı bulunamadı.</span>
        </div>

        <!-- Pagination -->
        <div *ngIf="!isLoadingUsers && totalPages > 1" class="pagination-container">
          <div class="pagination-info">
            <small>Toplam {{ totalUsers }} kullanıcı, {{ totalPages }} sayfa</small>
          </div>
          <div class="pagination-controls">
            <button type="button"
                    class="pagination-btn"
                    [disabled]="currentPage === 1"
                    (click)="onPageChange(currentPage - 1)">
              <i class="fas fa-chevron-left"></i>
            </button>
            <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
            <button type="button"
                    class="pagination-btn"
                    [disabled]="currentPage === totalPages"
                    (click)="onPageChange(currentPage + 1)">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- License Package Selection -->
      <div class="form-section mb-4">
        <h6 class="section-title">
          <i class="fas fa-box me-2"></i>Lisans Paketi Seçimi
        </h6>

        <div *ngIf="isLoading" class="loading-state">
          <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
          <span class="ms-2">Paketler yükleniyor...</span>
        </div>

        <div *ngIf="!isLoading" class="package-grid">
          <div class="package-card"
               *ngFor="let pkg of licensePackages"
               [class.selected]="purchaseForm.get('licensePackageID')?.value === pkg.licensePackageID"
               (click)="selectPackage(pkg)">
            <div class="package-header">
              <h6 class="package-name">{{ pkg.name }}</h6>
              <span class="package-price">{{ pkg.price | currency:'TRY':'symbol':'1.0-0' }}</span>
            </div>
            <div class="package-details">
              <div class="package-role">
                <i class="fas fa-user-tag me-1"></i>
                {{ pkg.role }}
              </div>
              <div class="package-duration">
                <i class="fas fa-clock me-1"></i>
                {{ pkg.durationDays }} gün
              </div>
              <div class="package-description">
                {{ pkg.description }}
              </div>
            </div>
            <div class="selection-indicator" *ngIf="purchaseForm.get('licensePackageID')?.value === pkg.licensePackageID">
              <i class="fas fa-check"></i>
            </div>
          </div>
        </div>

        <div *ngIf="!isLoading && licensePackages.length === 0" class="no-packages">
          <i class="fas fa-box-open me-2"></i>
          Uygun lisans paketi bulunamadı.
        </div>
      </div>

      <!-- Payment Method Selection -->
      <div class="form-section mb-4">
        <h6 class="section-title">
          <i class="fas fa-credit-card me-2"></i>Ödeme Yöntemi
        </h6>

        <div class="payment-methods">
          <div class="payment-method"
               *ngFor="let method of paymentMethods"
               [class.selected]="purchaseForm.get('paymentMethod')?.value === method"
               (click)="purchaseForm.patchValue({paymentMethod: method})">
            <div class="payment-icon">
              <i class="fas" [ngClass]="{
                'fa-money-bill-alt': method === 'Nakit',
                'fa-credit-card': method === 'Kredi Kartı',
                'fa-university': method === 'Havale/EFT'
              }"></i>
            </div>
            <span class="payment-label">{{ method }}</span>
            <div class="selection-indicator" *ngIf="purchaseForm.get('paymentMethod')?.value === method">
              <i class="fas fa-check"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Validation Errors -->
      <div class="form-errors" *ngIf="purchaseForm.invalid && (purchaseForm.dirty || purchaseForm.touched)">
        <div class="error-item" *ngIf="purchaseForm.get('userID')?.hasError('required')">
          <i class="fas fa-exclamation-circle me-2"></i>
          Lütfen bir kullanıcı seçin
        </div>
        <div class="error-item" *ngIf="purchaseForm.get('licensePackageID')?.hasError('required')">
          <i class="fas fa-exclamation-circle me-2"></i>
          Lütfen bir lisans paketi seçin
        </div>
        <div class="error-item" *ngIf="purchaseForm.get('paymentMethod')?.hasError('required')">
          <i class="fas fa-exclamation-circle me-2"></i>
          Lütfen bir ödeme yöntemi seçin
        </div>
      </div>
    </div>

    <div class="modern-card-footer">
      <button type="button"
              class="modern-btn modern-btn-outline-secondary"
              (click)="onCancel()"
              [disabled]="isSubmitting">
        <i class="fas fa-times me-2"></i>İptal
      </button>
      <button type="submit"
              class="modern-btn modern-btn-primary"
              [disabled]="purchaseForm.invalid || isSubmitting || !selectedUser">
        <span *ngIf="!isSubmitting">
          <i class="fas fa-shopping-cart me-2"></i>Satın Al
        </span>
        <span *ngIf="isSubmitting">
          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
          Satın alınıyor...
        </span>
      </button>
    </div>
  </form>
</div>
