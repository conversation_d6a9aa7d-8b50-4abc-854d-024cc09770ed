/* User Licenses List Component Styles */

/* Card Shadows */
.card {
  border: none;
  transition: all 0.3s ease;
}

.card.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Header Styling */
.card-header.bg-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  border-bottom: none;
}

.card-header h3 {
  font-weight: 600;
}

.card-header small {
  font-size: 0.875rem;
}

/* Filter Section */
.form-label {
  font-weight: 500;
  color: var(--bs-gray-700);
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
}

/* Table Styling */
.table {
  margin-bottom: 0;
}

.table-dark {
  background-color: #212529;
}

.table-dark th {
  border-color: #32383e;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 1rem 0.75rem;
}

.table tbody tr {
  transition: all 0.15s ease-in-out;
}

.table tbody tr:hover {
  background-color: rgba(13, 110, 253, 0.05);
}

.table td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border-color: #dee2e6;
}

/* Badge Styling */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
}

.badge.bg-info {
  background-color: #0dcaf0 !important;
  color: #000;
}

.badge.bg-secondary {
  background-color: #6c757d !important;
}

.badge.bg-danger {
  background-color: #dc3545 !important;
}

.badge.bg-warning {
  background-color: #ffc107 !important;
  color: #000;
}

.badge.bg-success {
  background-color: #198754 !important;
}

/* Button Styling */
.btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
}

/* Pagination */
.pagination {
  margin-bottom: 0;
}

.page-link {
  color: #007bff;
  border-color: #dee2e6;
  padding: 0.5rem 0.75rem;
}

.page-link:hover {
  color: #0056b3;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

.page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
}

/* Loading and Empty States */
.text-center i.fa-3x {
  font-size: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }

  .card-header .btn {
    width: 100%;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-group-sm > .btn {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .card {
    background-color: #2d3748;
    color: #e2e8f0;
  }

  .card-header:not(.bg-primary) {
    background-color: #4a5568 !important;
    color: #e2e8f0;
  }

  .form-control, .form-select {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }

  .form-control:focus, .form-select:focus {
    background-color: #4a5568;
    border-color: #63b3ed;
    color: #e2e8f0;
  }

  .input-group-text {
    background-color: #4a5568;
    border-color: #718096;
    color: #a0aec0;
  }

  .table {
    color: #e2e8f0;
  }

  .table tbody tr:hover {
    background-color: rgba(99, 179, 237, 0.1);
  }

  .table td {
    border-color: #4a5568;
  }

  .text-muted {
    color: #a0aec0 !important;
  }

  .page-link {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }

  .page-link:hover {
    background-color: #2d3748;
    border-color: #718096;
    color: #63b3ed;
  }

  .page-item.active .page-link {
    background-color: #3182ce;
    border-color: #3182ce;
  }
}

/* Custom Dark Mode Class Support */
[data-bs-theme="dark"] .card {
  background-color: #2d3748;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .card-header:not(.bg-primary) {
  background-color: #4a5568 !important;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
  background-color: #4a5568;
  border-color: #718096;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .form-control:focus,
[data-bs-theme="dark"] .form-select:focus {
  background-color: #4a5568;
  border-color: #63b3ed;
  color: #e2e8f0;
  box-shadow: 0 0 0 0.25rem rgba(99, 179, 237, 0.25);
}

[data-bs-theme="dark"] .input-group-text {
  background-color: #4a5568;
  border-color: #718096;
  color: #a0aec0;
}

[data-bs-theme="dark"] .table {
  color: #e2e8f0;
}

[data-bs-theme="dark"] .table tbody tr:hover {
  background-color: rgba(99, 179, 237, 0.1);
}

[data-bs-theme="dark"] .table td {
  border-color: #4a5568;
}

[data-bs-theme="dark"] .text-muted {
  color: #a0aec0 !important;
}

[data-bs-theme="dark"] .page-link {
  background-color: #4a5568;
  border-color: #718096;
  color: #e2e8f0;
}

[data-bs-theme="dark"] .page-link:hover {
  background-color: #2d3748;
  border-color: #718096;
  color: #63b3ed;
}

[data-bs-theme="dark"] .page-item.active .page-link {
  background-color: #3182ce;
  border-color: #3182ce;
}