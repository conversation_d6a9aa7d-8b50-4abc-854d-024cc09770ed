import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { UserLicenseService } from '../../services/user-license.service';
import { LicensePackageService } from '../../services/license-package.service';
import { UserLicenseDto } from '../../models/UserLicenseDto';
import { LicensePackage } from '../../models/licensePackage';

interface DialogData {
  userLicense: UserLicenseDto;
}

@Component({
  selector: 'app-extend-license',
  templateUrl: './extend-license.component.html',
  styleUrls: ['./extend-license.component.css'],
  standalone:false
})
export class ExtendLicenseComponent implements OnInit {
  extendForm: FormGroup;
  isSubmitting = false;
  licensePackages: LicensePackage[] = [];
  isLoadingPackages = false;
  extensionType: 'days' | 'package' = 'package'; // Default to package

  constructor(
    private fb: FormBuilder,
    private userLicenseService: UserLicenseService,
    private licensePackageService: LicensePackageService,
    private toastr: ToastrService,
    public dialogRef: MatDialogRef<ExtendLicenseComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    this.extendForm = this.fb.group({
      extensionType: ['package', Validators.required],
      extensionDays: [30, [Validators.required, Validators.min(1)]],
      licensePackageID: [null]
    });
  }

  ngOnInit(): void {
    this.loadLicensePackages();
    this.setupFormSubscriptions();
  }

  setupFormSubscriptions(): void {
    this.extendForm.get('extensionType')?.valueChanges.subscribe(value => {
      this.extensionType = value;
      if (value === 'package') {
        this.extendForm.get('licensePackageID')?.setValidators([Validators.required]);
        this.extendForm.get('extensionDays')?.clearValidators();
      } else {
        this.extendForm.get('extensionDays')?.setValidators([Validators.required, Validators.min(1)]);
        this.extendForm.get('licensePackageID')?.clearValidators();
      }
      this.extendForm.get('licensePackageID')?.updateValueAndValidity();
      this.extendForm.get('extensionDays')?.updateValueAndValidity();
    });
  }

  loadLicensePackages(): void {
    this.isLoadingPackages = true;
    this.licensePackageService.getAll().subscribe({
      next: (response) => {
        // Sadece aynı role sahip paketleri göster
        this.licensePackages = response.data.filter(pkg => pkg.role === this.data.userLicense.role);
        this.isLoadingPackages = false;
      },
      error: (error) => {
        this.toastr.error('Lisans paketleri yüklenirken bir hata oluştu', 'Hata');
        this.isLoadingPackages = false;
      }
    });
  }

  onSubmit(): void {
    if (this.extendForm.invalid) {
      this.toastr.error('Lütfen formu doğru şekilde doldurun', 'Hata');
      return;
    }

    this.isSubmitting = true;
    const formValue = this.extendForm.value;

    if (formValue.extensionType === 'package') {
      // Paket bazında uzatma
      this.userLicenseService.extendLicenseByPackage(
        this.data.userLicense.userLicenseID,
        formValue.licensePackageID
      ).subscribe({
        next: (response) => {
          this.toastr.success(response.message, 'Başarılı');
          this.dialogRef.close(true);
          this.isSubmitting = false;
        },
        error: (error) => {
          this.toastr.error('Lisans uzatılırken bir hata oluştu', 'Hata');
          this.isSubmitting = false;
        }
      });
    } else {
      // Gün bazında uzatma
      this.userLicenseService.extendLicense(
        this.data.userLicense.userLicenseID,
        formValue.extensionDays
      ).subscribe({
        next: (response) => {
          this.toastr.success(response.message, 'Başarılı');
          this.dialogRef.close(true);
          this.isSubmitting = false;
        },
        error: (error) => {
          this.toastr.error('Lisans uzatılırken bir hata oluştu', 'Hata');
          this.isSubmitting = false;
        }
      });
    }
  }

  getSelectedPackage(): LicensePackage | null {
    const packageId = this.extendForm.get('licensePackageID')?.value;
    return this.licensePackages.find(pkg => pkg.licensePackageID === packageId) || null;
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  getUserInitials(): string {
    const name = this.data.userLicense.userName;
    if (!name) return 'U';
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  }

  getRemainingDaysClass(): string {
    const days = this.data.userLicense.remainingDays;
    if (days <= 7) {
      return 'text-danger fw-bold';
    } else if (days <= 30) {
      return 'text-warning fw-bold';
    } else {
      return 'text-success';
    }
  }

  setExtensionType(type: 'days' | 'package'): void {
    this.extendForm.patchValue({ extensionType: type });
  }

  selectPackage(packageId: number): void {
    this.extendForm.patchValue({ licensePackageID: packageId });
  }
}
