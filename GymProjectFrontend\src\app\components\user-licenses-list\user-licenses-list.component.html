<div class="container-fluid mt-4">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <div>
            <h3 class="mb-0">
              <i class="fas fa-user-tag me-2"></i>Lisanslı Spor Salonları
            </h3>
            <small class="opacity-75">Aktif lisansa sahip spor salonlarını yönetin</small>
          </div>
          <button mat-raised-button color="accent" (click)="openPurchaseDialog()" class="btn-lg">
            <i class="fas fa-plus me-2"></i>Yeni <PERSON><PERSON>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>Filtreleme ve Arama
          </h5>
        </div>
        <div class="card-body">
          <form [formGroup]="filterForm">
            <div class="row g-3">
              <!-- Search -->
              <div class="col-md-4">
                <label class="form-label">Arama</label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input
                    type="text"
                    class="form-control"
                    formControlName="searchTerm"
                    placeholder="Email veya şirket adı ile ara...">
                </div>
              </div>

              <!-- Company Name Filter -->
              <div class="col-md-3">
                <label class="form-label">Şirket Adı</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="companyName"
                  placeholder="Şirket adı filtrele...">
              </div>

              <!-- Sort By -->
              <div class="col-md-3">
                <label class="form-label">Sıralama</label>
                <select class="form-select" formControlName="sortBy">
                  <option *ngFor="let option of sortOptions" [value]="option.value">
                    {{ option.label }}
                  </option>
                </select>
              </div>

              <!-- Clear Filters -->
              <div class="col-md-2 d-flex align-items-end">
                <button
                  type="button"
                  class="btn btn-outline-secondary w-100"
                  (click)="clearFilters()">
                  <i class="fas fa-times me-1"></i>Temizle
                </button>
              </div>
            </div>

            <!-- Remaining Days Filter -->
            <div class="row g-3 mt-2">
              <div class="col-md-3">
                <label class="form-label">Min. Kalan Gün</label>
                <input
                  type="number"
                  class="form-control"
                  formControlName="remainingDaysMin"
                  placeholder="Minimum gün"
                  min="0">
              </div>
              <div class="col-md-3">
                <label class="form-label">Max. Kalan Gün</label>
                <input
                  type="number"
                  class="form-control"
                  formControlName="remainingDaysMax"
                  placeholder="Maximum gün"
                  min="0">
              </div>
              <div class="col-md-2 d-flex align-items-end">
                <button
                  type="button"
                  class="btn btn-primary w-100"
                  (click)="applyRemainingDaysFilter()">
                  <i class="fas fa-filter me-1"></i>Uygula
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Results Section -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="mb-0">
              <i class="fas fa-list me-2"></i>Lisans Listesi
            </h5>
            <small class="text-muted" *ngIf="paginationData">
              Toplam {{ paginationData.totalCount }} kayıt bulundu
            </small>
          </div>
          <div class="d-flex align-items-center gap-2">
            <label class="form-label mb-0 me-2">Sayfa başına:</label>
            <select
              class="form-select form-select-sm"
              style="width: auto;"
              [value]="pageSize"
              (change)="onPageSizeChange(+($any($event.target).value))">
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>

        <div class="card-body p-0">
          <!-- Loading -->
          <div *ngIf="isLoading" class="d-flex justify-content-center p-5">
            <app-loading-spinner></app-loading-spinner>
          </div>

          <!-- No Data -->
          <div *ngIf="!isLoading && userLicenses.length === 0" class="text-center p-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Lisans bulunamadı</h5>
            <p class="text-muted">Arama kriterlerinizi değiştirmeyi deneyin.</p>
          </div>

          <!-- Data Table -->
          <div *ngIf="!isLoading && userLicenses.length > 0" class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-dark">
                <tr>
                  <th scope="col">
                    <i class="fas fa-building me-1"></i>Şirket
                  </th>
                  <th scope="col">
                    <i class="fas fa-user me-1"></i>Kullanıcı
                  </th>
                  <th scope="col">
                    <i class="fas fa-box me-1"></i>Paket
                  </th>
                  <th scope="col">
                    <i class="fas fa-user-tag me-1"></i>Rol
                  </th>
                  <th scope="col">
                    <i class="fas fa-calendar-alt me-1"></i>Başlangıç
                  </th>
                  <th scope="col">
                    <i class="fas fa-calendar-times me-1"></i>Bitiş
                  </th>
                  <th scope="col">
                    <i class="fas fa-clock me-1"></i>Kalan Gün
                  </th>
                  <th scope="col" class="text-center">
                    <i class="fas fa-cogs me-1"></i>İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of userLicenses; trackBy: trackByLicenseId">
                  <!-- Company Name -->
                  <td>
                    <div class="fw-bold text-primary">{{ item.companyName }}</div>
                  </td>

                  <!-- User Info -->
                  <td>
                    <div class="fw-bold">{{ item.userName }}</div>
                    <small class="text-muted">{{ item.userEmail }}</small>
                  </td>

                  <!-- Package -->
                  <td>
                    <span class="badge bg-info">{{ item.packageName }}</span>
                  </td>

                  <!-- Role -->
                  <td>
                    <span class="badge bg-secondary">{{ item.role }}</span>
                  </td>

                  <!-- Start Date -->
                  <td>
                    <small>{{ item.startDate | date:'dd/MM/yyyy' }}</small>
                  </td>

                  <!-- End Date -->
                  <td>
                    <small>{{ item.endDate | date:'dd/MM/yyyy' }}</small>
                  </td>

                  <!-- Remaining Days -->
                  <td>
                    <span [class]="getRemainingDaysBadgeClass(item.remainingDays)">
                      {{ item.remainingDays }} gün
                    </span>
                  </td>

                  <!-- Actions -->
                  <td class="text-center">
                    <div class="btn-group btn-group-sm" role="group">
                      <button
                        type="button"
                        class="btn btn-outline-primary"
                        (click)="openExtendDialog(item)"
                        title="Lisansı Uzat">
                        <i class="fas fa-calendar-plus"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-outline-danger"
                        (click)="revokeLicense(item.userLicenseID, item.userName)"
                        title="Lisansı İptal Et">
                        <i class="fas fa-times-circle"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination -->
        <div class="card-footer" *ngIf="paginationData && paginationData.totalPages > 1">
          <nav aria-label="Sayfa navigasyonu">
            <ul class="pagination justify-content-center mb-0">
              <!-- Previous Button -->
              <li class="page-item" [class.disabled]="!paginationData.hasPreviousPage">
                <button
                  class="page-link"
                  (click)="onPageChange(currentPage - 1)"
                  [disabled]="!paginationData.hasPreviousPage">
                  <i class="fas fa-chevron-left"></i>
                </button>
              </li>

              <!-- Page Numbers -->
              <li
                *ngFor="let page of getPageNumbers()"
                class="page-item"
                [class.active]="page === currentPage">
                <button class="page-link" (click)="onPageChange(page)">
                  {{ page }}
                </button>
              </li>

              <!-- Next Button -->
              <li class="page-item" [class.disabled]="!paginationData.hasNextPage">
                <button
                  class="page-link"
                  (click)="onPageChange(currentPage + 1)"
                  [disabled]="!paginationData.hasNextPage">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </li>
            </ul>
          </nav>

          <!-- Pagination Info -->
          <div class="text-center mt-2">
            <small class="text-muted">
              Sayfa {{ paginationData.pageNumber }} / {{ paginationData.totalPages }}
              ({{ paginationData.totalCount }} toplam kayıt)
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>