import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { FormBuilder, FormGroup } from '@angular/forms';
import { UserLicenseService } from '../../services/user-license.service';
import { DialogService } from '../../services/dialog.service';
import { UserLicenseDto } from '../../models/UserLicenseDto';
import { PaginatedUserLicenseDto } from '../../models/PaginatedUserLicenseDto';
import { LicensePurchaseComponent } from '../license-purchase/license-purchase.component';
import { ExtendLicenseComponent } from '../extend-license/extend-license.component';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'app-user-licenses-list',
  templateUrl: './user-licenses-list.component.html',
  styleUrls: ['./user-licenses-list.component.css'],
  standalone:false
})
export class UserLicensesListComponent implements OnInit {
  userLicenses: UserLicenseDto[] = [];
  paginationData: PaginatedUserLicenseDto | null = null;
  isLoading = false;
  displayedColumns: string[] = ['companyName', 'userName', 'packageName', 'role', 'startDate', 'endDate', 'remainingDays', 'actions'];

  // Pagination
  currentPage = 1;
  pageSize = 20;

  // Filtering and Search
  filterForm: FormGroup;
  sortOptions = [
    { value: 'newest', label: 'En Yeni Eklenen' },
    { value: 'oldest', label: 'En Eski Eklenen' },
    { value: 'expiring', label: 'Süresi Yakında Dolan' },
    { value: 'company', label: 'Şirket Adına Göre' }
  ];

  constructor(
    private userLicenseService: UserLicenseService,
    private dialogService: DialogService,
    private dialog: MatDialog,
    private toastr: ToastrService,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      searchTerm: [''],
      sortBy: ['newest'],
      companyName: [''],
      remainingDaysMin: [null],
      remainingDaysMax: [null]
    });
  }

  ngOnInit(): void {
    this.setupFormSubscriptions();
    this.loadUserLicenses();
  }

  setupFormSubscriptions(): void {
    // Search term değişikliklerini dinle
    this.filterForm.get('searchTerm')?.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged()
      )
      .subscribe(() => {
        this.currentPage = 1;
        this.loadUserLicenses();
      });

    // Diğer form değişikliklerini dinle
    this.filterForm.get('sortBy')?.valueChanges.subscribe(() => {
      this.currentPage = 1;
      this.loadUserLicenses();
    });

    this.filterForm.get('companyName')?.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged()
      )
      .subscribe(() => {
        this.currentPage = 1;
        this.loadUserLicenses();
      });
  }

  loadUserLicenses(): void {
    this.isLoading = true;
    const formValue = this.filterForm?.value || {};

    this.userLicenseService.getAllPaginated(
      this.currentPage,
      this.pageSize,
      formValue.searchTerm || '',
      formValue.sortBy || 'newest',
      formValue.companyName || '',
      formValue.remainingDaysMin,
      formValue.remainingDaysMax
    ).subscribe({
      next: (response) => {
        console.log('API Response:', response); // Debug log
        this.paginationData = response.data;
        this.userLicenses = response.data.data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('API Error:', error); // Debug log
        this.toastr.error('Kullanıcı lisansları yüklenirken bir hata oluştu: ' + (error.error?.message || error.message), 'Hata');
        this.isLoading = false;
      }
    });
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadUserLicenses();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = 1;
    this.loadUserLicenses();
  }

  clearFilters(): void {
    this.filterForm.reset({
      searchTerm: '',
      sortBy: 'newest',
      companyName: '',
      remainingDaysMin: null,
      remainingDaysMax: null
    });
    this.currentPage = 1;
    this.loadUserLicenses();
  }

  applyRemainingDaysFilter(): void {
    this.currentPage = 1;
    this.loadUserLicenses();
  }

  openPurchaseDialog(): void {
    const dialogRef = this.dialog.open(LicensePurchaseComponent, {
      width: '600px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUserLicenses();
      }
    });
  }

  openExtendDialog(userLicense: UserLicenseDto): void {
    const dialogRef = this.dialog.open(ExtendLicenseComponent, {
      width: '400px',
      data: { userLicense }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUserLicenses();
      }
    });
  }

  revokeLicense(id: number, userName: string): void {
    this.dialogService.confirmRevoke(userName).subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.userLicenseService.revokeLicense(id).subscribe({
          next: (response) => {
            this.toastr.success(response.message, 'Başarılı');
            this.loadUserLicenses();
          },
          error: (error) => {
            this.toastr.error('Lisans iptal edilirken bir hata oluştu', 'Hata');
          }
        });
      }
    });
  }

  getRemainingDaysClass(days: number): string {
    if (days <= 0) {
      return 'text-danger';
    } else if (days <= 7) {
      return 'text-warning';
    } else {
      return 'text-success';
    }
  }

  getRemainingDaysBadgeClass(remainingDays: number): string {
    if (remainingDays <= 7) {
      return 'badge bg-danger';
    } else if (remainingDays <= 30) {
      return 'badge bg-warning text-dark';
    } else {
      return 'badge bg-success';
    }
  }

  getPageNumbers(): number[] {
    if (!this.paginationData) return [];

    const totalPages = this.paginationData.totalPages;
    const currentPage = this.paginationData.pageNumber;
    const pages: number[] = [];

    // Show max 5 page numbers
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    if (endPage - startPage < 4) {
      startPage = Math.max(1, endPage - 4);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  trackByLicenseId(index: number, item: UserLicenseDto): number {
    return item.userLicenseID;
  }
}
